{"/api/analytics/post-view/route": "/api/analytics/post-view", "/api/clinics/[slug]/route": "/api/clinics/[slug]", "/api/clinics/route": "/api/clinics", "/api/protected-data/route": "/api/protected-data", "/api/protected-example/route": "/api/protected-example", "/api/revalidate/blog/route": "/api/revalidate/blog", "/api/revalidate/categories/route": "/api/revalidate/categories", "/api/revalidate/clinics/route": "/api/revalidate/clinics", "/api/revalidate/conditions/route": "/api/revalidate/conditions", "/api/revalidate/practitioners/route": "/api/revalidate/practitioners", "/api/revalidate/route": "/api/revalidate", "/api/revalidate/specialities/route": "/api/revalidate/specialities", "/api/secure-example/route": "/api/secure-example", "/api/strapi-proxy/route": "/api/strapi-proxy", "/api/test/route": "/api/test", "/sitemap-blog.xml/route": "/sitemap-blog.xml", "/sitemap-clinics.xml/route": "/sitemap-clinics.xml", "/sitemap-index.xml/route": "/sitemap-index.xml", "/sitemap-practitioners.xml/route": "/sitemap-practitioners.xml", "/sitemaps.xml/route": "/sitemaps.xml", "/test.xml/route": "/test.xml", "/blog/sitemap.xml/route": "/blog/sitemap.xml", "/clinics/sitemap.xml/route": "/clinics/sitemap.xml", "/practitioners/sitemap.xml/route": "/practitioners/sitemap.xml", "/robots.txt/route": "/robots.txt", "/sitemap.xml/route": "/sitemap.xml", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/about-us/page": "/about-us", "/account/page": "/account", "/affiliate-disclosure/page": "/affiliate-disclosure", "/api/example-server-component/page": "/api/example-server-component", "/blog/categories/page": "/blog/categories", "/blog/tags/[slug]/page": "/blog/tags/[slug]", "/blog/tags/page": "/blog/tags", "/optimized-client-example/page": "/optimized-client-example", "/page": "/", "/optimized-example/page": "/optimized-example", "/privacy/page": "/privacy", "/terms/page": "/terms", "/blog/authors/page": "/blog/authors", "/blog/[slug]/page": "/blog/[slug]", "/blog/authors/[slug]/page": "/blog/authors/[slug]", "/categories/[slug]/[cityStateSlug]/page": "/categories/[slug]/[cityStateSlug]", "/blog/page": "/blog", "/categories/page": "/categories", "/categories/[slug]/page": "/categories/[slug]", "/blog/categories/[slug]/page": "/blog/categories/[slug]", "/conditions/[slug]/page": "/conditions/[slug]", "/contact/page": "/contact", "/cors-test/page": "/cors-test", "/clinics/page": "/clinics", "/forgot-password/page": "/forgot-password", "/conditions/page": "/conditions", "/clinics/[slug]/page": "/clinics/[slug]", "/signin/page": "/signin", "/signup/page": "/signup", "/practitioners/[slug]/page": "/practitioners/[slug]", "/practitioners/page": "/practitioners", "/specialities/page": "/specialities", "/specialities/[slug]/page": "/specialities/[slug]", "/search/page": "/search"}